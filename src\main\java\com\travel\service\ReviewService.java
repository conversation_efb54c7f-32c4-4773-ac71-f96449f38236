package com.travel.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.travel.dto.review.CreateReviewRequest;
import com.travel.entity.Review;
import com.travel.vo.review.ReviewVO;

import java.math.BigDecimal;

/**
 * 评价服务接口
 * 
 * <AUTHOR> Team
 * @since 2024-01-01
 */
public interface ReviewService extends IService<Review> {

    /**
     * 创建评价
     */
    void createReview(Integer userId, CreateReviewRequest request);

    /**
     * 获取产品评价列表
     */
    IPage<ReviewVO> getProductReviews(Integer productId, Integer current, Integer size);

    /**
     * 获取用户评价列表
     */
    IPage<ReviewVO> getUserReviews(Integer userId, Integer current, Integer size);

    /**
     * 获取产品平均评分
     */
    BigDecimal getProductAverageRating(Integer productId);

    /**
     * 获取产品评价总数
     */
    Long getProductReviewCount(Integer productId);

    /**
     * 检查用户是否已评价产品
     */
    boolean hasUserReviewed(Integer userId, Integer productId);

    /**
     * 删除评价
     */
    void deleteReview(Integer reviewId, Integer userId);
}
