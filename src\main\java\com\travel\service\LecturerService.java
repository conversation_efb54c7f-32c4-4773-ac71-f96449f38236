package com.travel.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.travel.entity.Lecturer;

import java.util.List;

/**
 * 讲师信息服务接口
 * 
 * <AUTHOR> Team
 * @since 2024-01-01
 */
public interface LecturerService extends IService<Lecturer> {

    /**
     * 获取所有讲师列表
     */
    List<Lecturer> getAllLecturers();

    /**
     * 获取讲师详情
     */
    Lecturer getLecturerDetail(Integer lecturerId);

    /**
     * 根据专长搜索讲师
     */
    List<Lecturer> searchLecturersByExpertise(String expertise);
}
