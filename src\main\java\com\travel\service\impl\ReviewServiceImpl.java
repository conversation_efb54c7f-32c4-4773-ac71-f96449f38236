package com.travel.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.travel.common.BusinessException;
import com.travel.common.ResultCode;
import com.travel.dto.review.CreateReviewRequest;
import com.travel.entity.Product;
import com.travel.entity.Review;
import com.travel.entity.User;
import com.travel.mapper.ReviewMapper;
import com.travel.service.ProductService;
import com.travel.service.ReviewService;
import com.travel.service.UserService;
import com.travel.vo.review.ReviewVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 评价服务实现
 * 
 * <AUTHOR> Team
 * @since 2024-01-01
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ReviewServiceImpl extends ServiceImpl<ReviewMapper, Review> implements ReviewService {

    private final UserService userService;
    private final ProductService productService;

    @Override
    public void createReview(Integer userId, CreateReviewRequest request) {
        // 1. 验证用户和产品是否存在
        User user = userService.getUserDetail(userId);
        Product product = productService.getProductDetail(request.getProductId());
        
        // 2. 检查是否已经评价过
        if (hasUserReviewed(userId, request.getProductId())) {
            throw BusinessException.of("您已经评价过该产品");
        }
        
        // 3. 创建评价
        Review review = new Review();
        review.setUserId(userId);
        review.setProductId(request.getProductId());
        review.setContent(request.getContent());
        review.setRating(request.getRating());
        review.setCreatedAt(LocalDateTime.now());
        
        this.save(review);
        
        log.info("创建评价成功: reviewId={}, userId={}, productId={}", 
                review.getReviewId(), userId, request.getProductId());
    }

    @Override
    public IPage<ReviewVO> getProductReviews(Integer productId, Integer current, Integer size) {
        Page<Review> page = new Page<>(current, size);
        LambdaQueryWrapper<Review> wrapper = new LambdaQueryWrapper<Review>()
                .eq(Review::getProductId, productId)
                .orderByDesc(Review::getCreatedAt);
        
        IPage<Review> reviewPage = this.page(page, wrapper);
        return reviewPage.convert(this::convertToVO);
    }

    @Override
    public IPage<ReviewVO> getUserReviews(Integer userId, Integer current, Integer size) {
        Page<Review> page = new Page<>(current, size);
        LambdaQueryWrapper<Review> wrapper = new LambdaQueryWrapper<Review>()
                .eq(Review::getUserId, userId)
                .orderByDesc(Review::getCreatedAt);
        
        IPage<Review> reviewPage = this.page(page, wrapper);
        return reviewPage.convert(this::convertToVO);
    }

    @Override
    public BigDecimal getProductAverageRating(Integer productId) {
        BigDecimal avgRating = this.baseMapper.getAverageRating(productId);
        return avgRating != null ? avgRating : BigDecimal.ZERO;
    }

    @Override
    public Long getProductReviewCount(Integer productId) {
        Long count = this.baseMapper.getReviewCount(productId);
        return count != null ? count : 0L;
    }

    @Override
    public boolean hasUserReviewed(Integer userId, Integer productId) {
        long count = this.count(new LambdaQueryWrapper<Review>()
                .eq(Review::getUserId, userId)
                .eq(Review::getProductId, productId));
        return count > 0;
    }

    @Override
    public void deleteReview(Integer reviewId, Integer userId) {
        Review review = this.getById(reviewId);
        if (review == null) {
            throw BusinessException.of(ResultCode.NOT_FOUND, "评价不存在");
        }
        
        if (!review.getUserId().equals(userId)) {
            throw BusinessException.of(ResultCode.FORBIDDEN, "只能删除自己的评价");
        }
        
        this.removeById(reviewId);
        log.info("删除评价成功: reviewId={}, userId={}", reviewId, userId);
    }

    /**
     * 转换为VO
     */
    private ReviewVO convertToVO(Review review) {
        ReviewVO vo = new ReviewVO();
        vo.setReviewId(review.getReviewId());
        vo.setContent(review.getContent());
        vo.setRating(review.getRating());
        vo.setCreatedAt(review.getCreatedAt());
        
        // 获取用户信息
        try {
            User user = userService.getUserDetail(review.getUserId());
            ReviewVO.UserInfo userInfo = new ReviewVO.UserInfo();
            userInfo.setUserId(user.getUserId());
            userInfo.setNickname(user.getNickname());
            userInfo.setAvatarUrl(user.getAvatarUrl());
            vo.setUser(userInfo);
        } catch (Exception e) {
            log.warn("获取用户信息失败: userId={}", review.getUserId());
        }
        
        // 获取产品信息
        try {
            Product product = productService.getProductDetail(review.getProductId());
            ReviewVO.ProductInfo productInfo = new ReviewVO.ProductInfo();
            productInfo.setProductId(product.getProductId());
            productInfo.setName(product.getName());
            productInfo.setCoverUrl(product.getCoverUrl());
            vo.setProduct(productInfo);
        } catch (Exception e) {
            log.warn("获取产品信息失败: productId={}", review.getProductId());
        }
        
        return vo;
    }
}
