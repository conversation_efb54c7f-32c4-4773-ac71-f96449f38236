<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.travel.mapper.ProductMapper">

    <!-- 分页查询产品列表（包含城市和讲师信息） -->
    <select id="selectProductPageWithDetails" resultType="com.travel.entity.Product">
        SELECT 
            p.*,
            c.name as cityName,
            l.name as lecturerName,
            l.avatar_url as lecturerAvatar,
            l.title as lecturerTitle
        FROM product p
        LEFT JOIN city c ON p.city_id = c.city_id
        LEFT JOIN lecturer l ON p.lecturer_id = l.lecturer_id
        <where>
            <if test="cityId != null">
                AND p.city_id = #{cityId}
            </if>
            <if test="type != null">
                AND p.type = #{type}
            </if>
            <if test="keyword != null and keyword != ''">
                AND (
                    p.name LIKE CONCAT('%', #{keyword}, '%')
                    OR p.description LIKE CONCAT('%', #{keyword}, '%')
                    OR p.tags LIKE CONCAT('%', #{keyword}, '%')
                )
            </if>
        </where>
        ORDER BY p.created_at DESC
    </select>

</mapper>
