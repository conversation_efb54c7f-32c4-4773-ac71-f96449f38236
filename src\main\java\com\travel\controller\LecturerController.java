package com.travel.controller;

import com.travel.common.Result;
import com.travel.entity.Lecturer;
import com.travel.service.LecturerService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 讲师控制器
 * 
 * <AUTHOR> Team
 * @since 2024-01-01
 */
@Tag(name = "讲师管理", description = "讲师相关接口")
@RestController
@RequestMapping("/lecturer")
@RequiredArgsConstructor
public class LecturerController {

    private final LecturerService lecturerService;

    @Operation(summary = "获取所有讲师", description = "获取系统中所有讲师列表")
    @GetMapping("/list")
    public Result<List<Lecturer>> getAllLecturers() {
        List<Lecturer> lecturers = lecturerService.getAllLecturers();
        return Result.success(lecturers);
    }

    @Operation(summary = "获取讲师详情", description = "根据讲师ID获取详细信息")
    @GetMapping("/{lecturerId}")
    public Result<Lecturer> getLecturerDetail(
            @Parameter(description = "讲师ID", required = true) @PathVariable Integer lecturerId) {
        Lecturer lecturer = lecturerService.getLecturerDetail(lecturerId);
        return Result.success(lecturer);
    }

    @Operation(summary = "搜索讲师", description = "根据专长、姓名或头衔搜索讲师")
    @GetMapping("/search")
    public Result<List<Lecturer>> searchLecturers(
            @Parameter(description = "搜索关键词", required = true) @RequestParam String keyword) {
        List<Lecturer> lecturers = lecturerService.searchLecturersByExpertise(keyword);
        return Result.success(lecturers);
    }
}
