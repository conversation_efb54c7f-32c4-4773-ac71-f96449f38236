package com.travel.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.travel.entity.Review;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;

/**
 * 用户评价Mapper接口
 * 
 * <AUTHOR> Team
 * @since 2024-01-01
 */
@Mapper
public interface ReviewMapper extends BaseMapper<Review> {

    /**
     * 获取产品平均评分
     */
    BigDecimal getAverageRating(@Param("productId") Integer productId);

    /**
     * 获取产品评价总数
     */
    Long getReviewCount(@Param("productId") Integer productId);
}
