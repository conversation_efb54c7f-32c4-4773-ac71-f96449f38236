<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.travel.mapper.ReviewMapper">

    <!-- 获取产品平均评分 -->
    <select id="getAverageRating" resultType="java.math.BigDecimal">
        SELECT AVG(rating) 
        FROM review 
        WHERE product_id = #{productId}
    </select>

    <!-- 获取产品评价总数 -->
    <select id="getReviewCount" resultType="java.lang.Long">
        SELECT COUNT(*) 
        FROM review 
        WHERE product_id = #{productId}
    </select>

</mapper>
