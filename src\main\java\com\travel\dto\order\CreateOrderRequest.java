package com.travel.dto.order;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import java.math.BigDecimal;
import java.util.List;

/**
 * 创建订单请求DTO
 * 
 * <AUTHOR> Team
 * @since 2024-01-01
 */
@Data
@Schema(description = "创建订单请求")
public class CreateOrderRequest {

    @Schema(description = "订单项列表")
    @NotEmpty(message = "订单项不能为空")
    @Valid
    private List<OrderItemRequest> items;

    @Data
    @Schema(description = "订单项")
    public static class OrderItemRequest {
        
        @Schema(description = "产品ID", example = "1")
        @NotNull(message = "产品ID不能为空")
        private Integer productId;

        @Schema(description = "SKU名称", example = "成人票")
        @NotNull(message = "SKU名称不能为空")
        private String skuName;

        @Schema(description = "单价", example = "29.90")
        @NotNull(message = "单价不能为空")
        @Positive(message = "单价必须大于0")
        private BigDecimal price;

        @Schema(description = "数量", example = "1")
        @NotNull(message = "数量不能为空")
        @Positive(message = "数量必须大于0")
        private Integer quantity;
    }
}
