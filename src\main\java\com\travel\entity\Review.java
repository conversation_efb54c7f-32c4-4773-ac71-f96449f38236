package com.travel.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 用户评价实体
 * 
 * <AUTHOR> Team
 * @since 2024-01-01
 */
@Data
@TableName("review")
@Schema(description = "用户评价")
public class Review implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "评价ID")
    @TableId(value = "review_id", type = IdType.AUTO)
    private Integer reviewId;

    @Schema(description = "用户ID")
    @TableField("user_id")
    private Integer userId;

    @Schema(description = "产品ID")
    @TableField("product_id")
    private Integer productId;

    @Schema(description = "评价内容")
    @TableField("content")
    private String content;

    @Schema(description = "评分(1-5)")
    @TableField("rating")
    private Integer rating;

    @Schema(description = "创建时间")
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;
}
