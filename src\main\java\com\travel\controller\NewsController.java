package com.travel.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.travel.common.PageResult;
import com.travel.common.Result;
import com.travel.entity.News;
import com.travel.service.NewsService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * 资讯控制器
 * 
 * <AUTHOR> Team
 * @since 2024-01-01
 */
@Tag(name = "资讯管理", description = "资讯相关接口")
@RestController
@RequestMapping("/news")
@RequiredArgsConstructor
public class NewsController {

    private final NewsService newsService;

    @Operation(summary = "分页获取资讯列表", description = "支持按城市筛选")
    @GetMapping("/page")
    public Result<PageResult<News>> getNewsPage(
            @Parameter(description = "当前页码", example = "1") @RequestParam(defaultValue = "1") Integer current,
            @Parameter(description = "每页大小", example = "10") @RequestParam(defaultValue = "10") Integer size,
            @Parameter(description = "城市ID") @RequestParam(required = false) Integer cityId) {
        
        IPage<News> page = newsService.getNewsPage(current, size, cityId);
        return Result.success(PageResult.of(page));
    }

    @Operation(summary = "获取资讯详情", description = "根据资讯ID获取详细信息")
    @GetMapping("/{newsId}")
    public Result<News> getNewsDetail(
            @Parameter(description = "资讯ID", required = true) @PathVariable Integer newsId) {
        News news = newsService.getNewsDetail(newsId);
        return Result.success(news);
    }

    @Operation(summary = "根据城市获取资讯", description = "获取指定城市的资讯列表")
    @GetMapping("/city/{cityId}")
    public Result<PageResult<News>> getNewsByCity(
            @Parameter(description = "城市ID", required = true) @PathVariable Integer cityId,
            @Parameter(description = "当前页码", example = "1") @RequestParam(defaultValue = "1") Integer current,
            @Parameter(description = "每页大小", example = "10") @RequestParam(defaultValue = "10") Integer size) {
        
        IPage<News> page = newsService.getNewsByCity(current, size, cityId);
        return Result.success(PageResult.of(page));
    }

    @Operation(summary = "搜索资讯", description = "根据关键词搜索资讯")
    @GetMapping("/search")
    public Result<PageResult<News>> searchNews(
            @Parameter(description = "搜索关键词", required = true) @RequestParam String keyword,
            @Parameter(description = "当前页码", example = "1") @RequestParam(defaultValue = "1") Integer current,
            @Parameter(description = "每页大小", example = "10") @RequestParam(defaultValue = "10") Integer size) {
        
        IPage<News> page = newsService.searchNews(current, size, keyword);
        return Result.success(PageResult.of(page));
    }
}
