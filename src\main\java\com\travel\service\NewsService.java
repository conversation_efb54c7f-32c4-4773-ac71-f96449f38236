package com.travel.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.travel.entity.News;

/**
 * 资讯信息服务接口
 * 
 * <AUTHOR> Team
 * @since 2024-01-01
 */
public interface NewsService extends IService<News> {

    /**
     * 分页获取资讯列表
     */
    IPage<News> getNewsPage(Integer current, Integer size, Integer cityId);

    /**
     * 获取资讯详情
     */
    News getNewsDetail(Integer newsId);

    /**
     * 根据城市获取资讯
     */
    IPage<News> getNewsByCity(Integer current, Integer size, Integer cityId);

    /**
     * 搜索资讯
     */
    IPage<News> searchNews(Integer current, Integer size, String keyword);
}
