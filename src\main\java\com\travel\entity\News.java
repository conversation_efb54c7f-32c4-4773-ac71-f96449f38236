package com.travel.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 资讯信息实体
 * 
 * <AUTHOR> Team
 * @since 2024-01-01
 */
@Data
@TableName("news")
@Schema(description = "资讯信息")
public class News implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "资讯ID")
    @TableId(value = "news_id", type = IdType.AUTO)
    private Integer newsId;

    @Schema(description = "城市ID")
    @TableField("city_id")
    private Integer cityId;

    @Schema(description = "资讯标题")
    @TableField("title")
    private String title;

    @Schema(description = "资讯内容")
    @TableField("content")
    private String content;

    @Schema(description = "封面图URL")
    @TableField("cover_url")
    private String coverUrl;

    @Schema(description = "创建时间")
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;
}
