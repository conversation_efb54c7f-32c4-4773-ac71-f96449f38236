package com.travel.vo.order;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 订单信息VO
 * 
 * <AUTHOR> Team
 * @since 2024-01-01
 */
@Data
@Schema(description = "订单信息")
public class OrderVO {

    @Schema(description = "订单ID", example = "1")
    private Integer orderId;

    @Schema(description = "订单号", example = "TRV202401010001")
    private String orderNo;

    @Schema(description = "订单总额", example = "59.80")
    private BigDecimal totalAmount;

    @Schema(description = "订单状态", example = "0")
    private Integer status;

    @Schema(description = "订单状态名称", example = "待付款")
    private String statusName;

    @Schema(description = "创建时间")
    private LocalDateTime createdAt;

    @Schema(description = "更新时间")
    private LocalDateTime updatedAt;

    @Schema(description = "订单项列表")
    private List<OrderItemVO> items;

    @Data
    @Schema(description = "订单项信息")
    public static class OrderItemVO {
        
        @Schema(description = "明细ID", example = "1")
        private Integer itemId;

        @Schema(description = "产品ID", example = "1")
        private Integer productId;

        @Schema(description = "产品名称", example = "故宫博物院讲解包")
        private String productName;

        @Schema(description = "产品封面", example = "https://example.com/cover.jpg")
        private String productCover;

        @Schema(description = "SKU名称", example = "成人票")
        private String skuName;

        @Schema(description = "单价", example = "29.90")
        private BigDecimal price;

        @Schema(description = "数量", example = "2")
        private Integer quantity;

        @Schema(description = "小计", example = "59.80")
        private BigDecimal subtotal;
    }

    /**
     * 获取订单状态名称
     */
    public String getStatusName() {
        if (status == null) return "未知";
        switch (status) {
            case 0:
                return "待付款";
            case 1:
                return "已付款";
            case 2:
                return "已取消";
            case 3:
                return "退款中";
            case 4:
                return "已退款";
            default:
                return "未知";
        }
    }
}
