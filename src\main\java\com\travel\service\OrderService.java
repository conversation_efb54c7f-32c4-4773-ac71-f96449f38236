package com.travel.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.travel.dto.order.CreateOrderRequest;
import com.travel.entity.Order;
import com.travel.vo.order.OrderVO;

/**
 * 订单服务接口
 * 
 * <AUTHOR> Team
 * @since 2024-01-01
 */
public interface OrderService extends IService<Order> {

    /**
     * 创建订单
     */
    OrderVO createOrder(Integer userId, CreateOrderRequest request);

    /**
     * 获取用户订单列表
     */
    IPage<OrderVO> getUserOrders(Integer userId, Integer current, Integer size, Integer status);

    /**
     * 获取订单详情
     */
    OrderVO getOrderDetail(Integer orderId, Integer userId);

    /**
     * 取消订单
     */
    void cancelOrder(Integer orderId, Integer userId);

    /**
     * 支付订单
     */
    void payOrder(Integer orderId, Integer userId);

    /**
     * 申请退款
     */
    void refundOrder(Integer orderId, Integer userId);

    /**
     * 根据订单号获取订单
     */
    Order getOrderByNo(String orderNo);

    /**
     * 更新订单状态
     */
    void updateOrderStatus(Integer orderId, Integer status);
}
