package com.travel.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.travel.entity.OrderItem;

import java.util.List;

/**
 * 订单明细服务接口
 * 
 * <AUTHOR> Team
 * @since 2024-01-01
 */
public interface OrderItemService extends IService<OrderItem> {

    /**
     * 根据订单ID获取订单项列表
     */
    List<OrderItem> getItemsByOrderId(Integer orderId);

    /**
     * 根据订单ID删除订单项
     */
    void deleteByOrderId(Integer orderId);
}
