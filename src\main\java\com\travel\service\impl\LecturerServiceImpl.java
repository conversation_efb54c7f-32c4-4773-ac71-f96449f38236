package com.travel.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.travel.common.BusinessException;
import com.travel.common.ResultCode;
import com.travel.entity.Lecturer;
import com.travel.mapper.LecturerMapper;
import com.travel.service.LecturerService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 讲师信息服务实现
 * 
 * <AUTHOR> Team
 * @since 2024-01-01
 */
@Slf4j
@Service
public class LecturerServiceImpl extends ServiceImpl<LecturerMapper, Lecturer> implements LecturerService {

    @Override
    public List<Lecturer> getAllLecturers() {
        return this.list();
    }

    @Override
    public Lecturer getLecturerDetail(Integer lecturerId) {
        Lecturer lecturer = this.getById(lecturerId);
        if (lecturer == null) {
            throw BusinessException.of(ResultCode.NOT_FOUND, "讲师不存在");
        }
        return lecturer;
    }

    @Override
    public List<Lecturer> searchLecturersByExpertise(String expertise) {
        if (StringUtils.isBlank(expertise)) {
            return getAllLecturers();
        }
        
        LambdaQueryWrapper<Lecturer> wrapper = new LambdaQueryWrapper<Lecturer>()
                .like(Lecturer::getExpertise, expertise)
                .or()
                .like(Lecturer::getName, expertise)
                .or()
                .like(Lecturer::getTitle, expertise);
        return this.list(wrapper);
    }
}
