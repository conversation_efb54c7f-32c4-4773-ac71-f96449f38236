package com.travel.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.travel.common.PageResult;
import com.travel.common.Result;
import com.travel.dto.order.CreateOrderRequest;
import com.travel.service.OrderService;
import com.travel.vo.order.OrderVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;

/**
 * 订单控制器
 * 
 * <AUTHOR> Team
 * @since 2024-01-01
 */
@Tag(name = "订单管理", description = "订单相关接口")
@RestController
@RequestMapping("/order")
@RequiredArgsConstructor
public class OrderController {

    private final OrderService orderService;

    @Operation(summary = "创建订单", description = "创建新订单")
    @PostMapping("/create")
    public Result<OrderVO> createOrder(
            @Parameter(description = "用户ID", required = true) @RequestParam Integer userId,
            @Valid @RequestBody CreateOrderRequest request) {
        OrderVO order = orderService.createOrder(userId, request);
        return Result.success("订单创建成功", order);
    }

    @Operation(summary = "获取用户订单列表", description = "分页获取用户订单列表")
    @GetMapping("/list")
    public Result<PageResult<OrderVO>> getUserOrders(
            @Parameter(description = "用户ID", required = true) @RequestParam Integer userId,
            @Parameter(description = "当前页码", example = "1") @RequestParam(defaultValue = "1") Integer current,
            @Parameter(description = "每页大小", example = "10") @RequestParam(defaultValue = "10") Integer size,
            @Parameter(description = "订单状态(0待付款 1已付款 2已取消 3退款中 4已退款)") @RequestParam(required = false) Integer status) {
        
        IPage<OrderVO> page = orderService.getUserOrders(userId, current, size, status);
        return Result.success(PageResult.of(page));
    }

    @Operation(summary = "获取订单详情", description = "根据订单ID获取详细信息")
    @GetMapping("/{orderId}")
    public Result<OrderVO> getOrderDetail(
            @Parameter(description = "订单ID", required = true) @PathVariable Integer orderId,
            @Parameter(description = "用户ID", required = true) @RequestParam Integer userId) {
        OrderVO order = orderService.getOrderDetail(orderId, userId);
        return Result.success(order);
    }

    @Operation(summary = "取消订单", description = "取消待付款订单")
    @PostMapping("/{orderId}/cancel")
    public Result<Void> cancelOrder(
            @Parameter(description = "订单ID", required = true) @PathVariable Integer orderId,
            @Parameter(description = "用户ID", required = true) @RequestParam Integer userId) {
        orderService.cancelOrder(orderId, userId);
        return Result.success("订单取消成功");
    }

    @Operation(summary = "支付订单", description = "支付订单（模拟支付）")
    @PostMapping("/{orderId}/pay")
    public Result<Void> payOrder(
            @Parameter(description = "订单ID", required = true) @PathVariable Integer orderId,
            @Parameter(description = "用户ID", required = true) @RequestParam Integer userId) {
        orderService.payOrder(orderId, userId);
        return Result.success("支付成功");
    }

    @Operation(summary = "申请退款", description = "申请订单退款")
    @PostMapping("/{orderId}/refund")
    public Result<Void> refundOrder(
            @Parameter(description = "订单ID", required = true) @PathVariable Integer orderId,
            @Parameter(description = "用户ID", required = true) @RequestParam Integer userId) {
        orderService.refundOrder(orderId, userId);
        return Result.success("退款申请提交成功");
    }
}
