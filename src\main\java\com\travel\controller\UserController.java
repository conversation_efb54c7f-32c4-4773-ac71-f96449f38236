package com.travel.controller;

import com.travel.common.Result;
import com.travel.entity.User;
import com.travel.service.UserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * 用户控制器
 * 
 * <AUTHOR> Team
 * @since 2024-01-01
 */
@Tag(name = "用户管理", description = "用户相关接口")
@RestController
@RequestMapping("/user")
@RequiredArgsConstructor
public class UserController {

    private final UserService userService;

    @Operation(summary = "获取用户信息", description = "根据用户ID获取用户详细信息")
    @GetMapping("/{userId}")
    public Result<User> getUserDetail(
            @Parameter(description = "用户ID", required = true) @PathVariable Integer userId) {
        User user = userService.getUserDetail(userId);
        return Result.success(user);
    }

    @Operation(summary = "更新用户信息", description = "更新用户基本信息")
    @PutMapping("/{userId}")
    public Result<Void> updateUser(
            @Parameter(description = "用户ID", required = true) @PathVariable Integer userId,
            @RequestBody User user) {
        user.setUserId(userId);
        userService.updateById(user);
        return Result.success("更新成功");
    }
}
