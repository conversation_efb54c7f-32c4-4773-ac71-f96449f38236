package com.travel.dto.review;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.*;

/**
 * 创建评价请求DTO
 * 
 * <AUTHOR> Team
 * @since 2024-01-01
 */
@Data
@Schema(description = "创建评价请求")
public class CreateReviewRequest {

    @Schema(description = "产品ID", example = "1")
    @NotNull(message = "产品ID不能为空")
    private Integer productId;

    @Schema(description = "评价内容", example = "讲解很详细，值得推荐！")
    @NotBlank(message = "评价内容不能为空")
    @Size(max = 500, message = "评价内容不能超过500字")
    private String content;

    @Schema(description = "评分(1-5)", example = "5")
    @NotNull(message = "评分不能为空")
    @Min(value = 1, message = "评分最低为1分")
    @Max(value = 5, message = "评分最高为5分")
    private Integer rating;
}
