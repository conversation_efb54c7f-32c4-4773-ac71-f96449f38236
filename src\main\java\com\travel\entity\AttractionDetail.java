package com.travel.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * 景区详情实体
 * 
 * <AUTHOR> Team
 * @since 2024-01-01
 */
@Data
@TableName("attraction_detail")
@Schema(description = "景区详情")
public class AttractionDetail implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "详情ID")
    @TableId(value = "detail_id", type = IdType.AUTO)
    private Integer detailId;

    @Schema(description = "产品ID")
    @TableField("product_id")
    private Integer productId;

    @Schema(description = "详细地址")
    @TableField("address")
    private String address;

    @Schema(description = "营业时间")
    @TableField("open_hours")
    private String openHours;

    @Schema(description = "景区地图URL")
    @TableField("map_url")
    private String mapUrl;

    @Schema(description = "入口指引")
    @TableField("entrance_guide")
    private String entranceGuide;

    @Schema(description = "讲解视频URL")
    @TableField("video_url")
    private String videoUrl;

    @Schema(description = "视频时长")
    @TableField("video_duration")
    private String videoDuration;
}
