package com.travel.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.travel.common.BusinessException;
import com.travel.common.ResultCode;
import com.travel.dto.order.CreateOrderRequest;
import com.travel.entity.Order;
import com.travel.entity.OrderItem;
import com.travel.entity.Product;
import com.travel.mapper.OrderMapper;
import com.travel.service.OrderItemService;
import com.travel.service.OrderService;
import com.travel.service.ProductService;
import com.travel.vo.order.OrderVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 订单服务实现
 * 
 * <AUTHOR> Team
 * @since 2024-01-01
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OrderServiceImpl extends ServiceImpl<OrderMapper, Order> implements OrderService {

    private final OrderItemService orderItemService;
    private final ProductService productService;
    
    private static final AtomicLong ORDER_SEQUENCE = new AtomicLong(1);

    @Override
    @Transactional(rollbackFor = Exception.class)
    public OrderVO createOrder(Integer userId, CreateOrderRequest request) {
        // 1. 验证产品信息并计算总金额
        BigDecimal totalAmount = BigDecimal.ZERO;
        List<OrderItem> orderItems = new ArrayList<>();
        
        for (CreateOrderRequest.OrderItemRequest itemRequest : request.getItems()) {
            Product product = productService.getProductDetail(itemRequest.getProductId());
            if (product == null) {
                throw BusinessException.of(ResultCode.PRODUCT_NOT_FOUND);
            }
            
            // 计算小计
            BigDecimal subtotal = itemRequest.getPrice().multiply(new BigDecimal(itemRequest.getQuantity()));
            totalAmount = totalAmount.add(subtotal);
            
            // 创建订单项（暂时不设置orderId）
            OrderItem orderItem = new OrderItem();
            orderItem.setProductId(itemRequest.getProductId());
            orderItem.setSkuName(itemRequest.getSkuName());
            orderItem.setPrice(itemRequest.getPrice());
            orderItem.setQuantity(itemRequest.getQuantity());
            orderItems.add(orderItem);
        }
        
        // 2. 创建订单
        Order order = new Order();
        order.setUserId(userId);
        order.setOrderNo(generateOrderNo());
        order.setTotalAmount(totalAmount);
        order.setStatus(0); // 待付款
        order.setCreatedAt(LocalDateTime.now());
        order.setUpdatedAt(LocalDateTime.now());
        
        this.save(order);
        
        // 3. 创建订单项
        for (OrderItem orderItem : orderItems) {
            orderItem.setOrderId(order.getOrderId());
        }
        orderItemService.saveBatch(orderItems);
        
        log.info("创建订单成功: orderId={}, orderNo={}, userId={}", 
                order.getOrderId(), order.getOrderNo(), userId);
        
        // 4. 返回订单详情
        return getOrderDetail(order.getOrderId(), userId);
    }

    @Override
    public IPage<OrderVO> getUserOrders(Integer userId, Integer current, Integer size, Integer status) {
        Page<Order> page = new Page<>(current, size);
        LambdaQueryWrapper<Order> wrapper = new LambdaQueryWrapper<Order>()
                .eq(Order::getUserId, userId);
        
        if (status != null) {
            wrapper.eq(Order::getStatus, status);
        }
        
        wrapper.orderByDesc(Order::getCreatedAt);
        IPage<Order> orderPage = this.page(page, wrapper);
        
        // 转换为VO
        IPage<OrderVO> voPage = orderPage.convert(this::convertToVO);
        return voPage;
    }

    @Override
    public OrderVO getOrderDetail(Integer orderId, Integer userId) {
        Order order = this.getById(orderId);
        if (order == null || !order.getUserId().equals(userId)) {
            throw BusinessException.of(ResultCode.ORDER_NOT_FOUND);
        }
        
        return convertToVO(order);
    }

    @Override
    public void cancelOrder(Integer orderId, Integer userId) {
        Order order = this.getById(orderId);
        if (order == null || !order.getUserId().equals(userId)) {
            throw BusinessException.of(ResultCode.ORDER_NOT_FOUND);
        }
        
        if (!order.getStatus().equals(0)) {
            throw BusinessException.of(ResultCode.ORDER_STATUS_ERROR, "只能取消待付款订单");
        }
        
        order.setStatus(2); // 已取消
        order.setUpdatedAt(LocalDateTime.now());
        this.updateById(order);
        
        log.info("取消订单成功: orderId={}, userId={}", orderId, userId);
    }

    @Override
    public void payOrder(Integer orderId, Integer userId) {
        Order order = this.getById(orderId);
        if (order == null || !order.getUserId().equals(userId)) {
            throw BusinessException.of(ResultCode.ORDER_NOT_FOUND);
        }
        
        if (!order.getStatus().equals(0)) {
            throw BusinessException.of(ResultCode.ORDER_STATUS_ERROR, "订单状态错误");
        }
        
        order.setStatus(1); // 已付款
        order.setUpdatedAt(LocalDateTime.now());
        this.updateById(order);
        
        log.info("支付订单成功: orderId={}, userId={}", orderId, userId);
    }

    @Override
    public void refundOrder(Integer orderId, Integer userId) {
        Order order = this.getById(orderId);
        if (order == null || !order.getUserId().equals(userId)) {
            throw BusinessException.of(ResultCode.ORDER_NOT_FOUND);
        }
        
        if (!order.getStatus().equals(1)) {
            throw BusinessException.of(ResultCode.ORDER_STATUS_ERROR, "只能退款已付款订单");
        }
        
        order.setStatus(3); // 退款中
        order.setUpdatedAt(LocalDateTime.now());
        this.updateById(order);
        
        log.info("申请退款成功: orderId={}, userId={}", orderId, userId);
    }

    @Override
    public Order getOrderByNo(String orderNo) {
        return this.getOne(new LambdaQueryWrapper<Order>()
                .eq(Order::getOrderNo, orderNo));
    }

    @Override
    public void updateOrderStatus(Integer orderId, Integer status) {
        Order order = this.getById(orderId);
        if (order == null) {
            throw BusinessException.of(ResultCode.ORDER_NOT_FOUND);
        }
        
        order.setStatus(status);
        order.setUpdatedAt(LocalDateTime.now());
        this.updateById(order);
    }

    /**
     * 生成订单号
     */
    private String generateOrderNo() {
        String dateStr = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        long sequence = ORDER_SEQUENCE.getAndIncrement();
        return "TRV" + dateStr + String.format("%06d", sequence);
    }

    /**
     * 转换为VO
     */
    private OrderVO convertToVO(Order order) {
        OrderVO vo = new OrderVO();
        vo.setOrderId(order.getOrderId());
        vo.setOrderNo(order.getOrderNo());
        vo.setTotalAmount(order.getTotalAmount());
        vo.setStatus(order.getStatus());
        vo.setStatusName(vo.getStatusName());
        vo.setCreatedAt(order.getCreatedAt());
        vo.setUpdatedAt(order.getUpdatedAt());
        
        // 获取订单项
        List<OrderItem> items = orderItemService.getItemsByOrderId(order.getOrderId());
        List<OrderVO.OrderItemVO> itemVOs = new ArrayList<>();
        
        for (OrderItem item : items) {
            OrderVO.OrderItemVO itemVO = new OrderVO.OrderItemVO();
            itemVO.setItemId(item.getItemId());
            itemVO.setProductId(item.getProductId());
            itemVO.setSkuName(item.getSkuName());
            itemVO.setPrice(item.getPrice());
            itemVO.setQuantity(item.getQuantity());
            itemVO.setSubtotal(item.getPrice().multiply(new BigDecimal(item.getQuantity())));
            
            // 获取产品信息
            try {
                Product product = productService.getProductDetail(item.getProductId());
                itemVO.setProductName(product.getName());
                itemVO.setProductCover(product.getCoverUrl());
            } catch (Exception e) {
                log.warn("获取产品信息失败: productId={}", item.getProductId());
            }
            
            itemVOs.add(itemVO);
        }
        
        vo.setItems(itemVOs);
        return vo;
    }
}
