# 旅游讲解小程序后端服务

## 项目简介

这是一个基于Spring Boot 3.x开发的微信小程序旅游讲解后端服务，提供完整的旅游产品管理、用户认证、订单处理、评价系统等功能。

## 技术栈

- **框架**: Spring Boot 3.2.0
- **安全**: Spring Security + JWT
- **数据库**: MySQL 8.0 + MyBatis-Plus 3.5.4
- **缓存**: Redis
- **文档**: Knife4j 4.3.0 (Swagger)
- **微信**: weixin-java-miniapp 4.5.0

## 核心功能

### 1. 用户认证系统
- 微信小程序登录
- 手机号授权
- JWT令牌管理
- 用户会话管理

### 2. 产品管理
- 产品列表查询（支持分页、筛选、搜索）
- 产品详情展示
- 城市分类管理
- 讲师信息管理

### 3. 讲解系统
- 景区分类管理
- 讲解点信息
- 音频内容管理
- 图片和文本内容

### 4. 订单系统
- 订单创建和管理
- 支付流程（模拟）
- 订单状态跟踪
- 退款处理

### 5. 评价系统
- 用户评价和评分
- 产品评价统计
- 评价列表展示

### 6. 资讯系统
- 城市资讯管理
- 资讯内容展示

## 项目结构

```
src/main/java/com/travel/
├── TravelApplication.java          # 启动类
├── common/                         # 通用组件
│   ├── Result.java                # 统一响应结果
│   ├── PageResult.java            # 分页响应结果
│   ├── BusinessException.java     # 业务异常
│   └── GlobalExceptionHandler.java # 全局异常处理
├── config/                         # 配置类
│   ├── Knife4jConfig.java         # API文档配置
│   ├── MybatisPlusConfig.java     # MyBatis-Plus配置
│   ├── SecurityConfig.java        # 安全配置
│   └── WeChatConfig.java          # 微信配置
├── controller/                     # 控制器
├── dto/                           # 数据传输对象
├── entity/                        # 实体类
├── mapper/                        # 数据访问层
├── security/                      # 安全组件
├── service/                       # 业务逻辑层
├── utils/                         # 工具类
└── vo/                           # 视图对象
```

## 环境配置

### 1. 数据库配置

创建MySQL数据库并执行`travel.sql`脚本：

```sql
CREATE DATABASE travel CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci;
```

### 2. 应用配置

修改`application.yml`中的配置：

```yaml
spring:
  datasource:
    url: **********************************
    username: your_username
    password: your_password
  data:
    redis:
      host: localhost
      port: 6379

wechat:
  miniapp:
    app-id: your_wechat_app_id
    secret: your_wechat_secret

jwt:
  secret: your_jwt_secret_key
```

### 3. 环境变量（可选）

```bash
export DB_USERNAME=root
export DB_PASSWORD=123456
export REDIS_HOST=localhost
export REDIS_PORT=6379
export WECHAT_APPID=your_app_id
export WECHAT_SECRET=your_secret
export JWT_SECRET=your_jwt_secret
```

## 运行项目

### 1. 使用Maven运行

```bash
mvn spring-boot:run
```

### 2. 使用IDE运行

直接运行`TravelApplication.java`主类

### 3. 打包运行

```bash
mvn clean package
java -jar target/miniprogram-backend-1.0.0.jar
```

## API文档

项目启动后，访问以下地址查看API文档：

- **Knife4j文档**: http://localhost:8080/api/doc.html
- **Swagger UI**: http://localhost:8080/api/swagger-ui/index.html

## 主要API端点

### 认证相关
- `POST /api/auth/wechat/login` - 微信登录
- `POST /api/auth/phone/authorize` - 手机号授权
- `POST /api/auth/refresh` - 刷新令牌
- `POST /api/auth/logout` - 用户登出

### 产品相关
- `GET /api/product/page` - 分页查询产品
- `GET /api/product/{id}` - 获取产品详情
- `GET /api/product/hot` - 获取热门产品
- `GET /api/product/search` - 搜索产品

### 订单相关
- `POST /api/order/create` - 创建订单
- `GET /api/order/list` - 获取用户订单
- `POST /api/order/{id}/pay` - 支付订单
- `POST /api/order/{id}/cancel` - 取消订单

### 评价相关
- `POST /api/review/create` - 创建评价
- `GET /api/review/product/{id}` - 获取产品评价
- `GET /api/review/stats/{id}` - 获取评价统计

## 数据库设计

项目包含以下主要数据表：

- `user` - 用户信息
- `city` - 城市信息
- `product` - 产品信息
- `lecturer` - 讲师信息
- `attraction_category` - 景区分类
- `explanation_point` - 讲解点
- `order` / `order_item` - 订单相关
- `review` - 用户评价
- `news` - 资讯信息

## 安全机制

- JWT令牌认证
- Spring Security权限控制
- CORS跨域配置
- 输入参数验证
- SQL注入防护

## 开发规范

- 统一的响应格式
- 完整的异常处理
- 详细的API文档
- 规范的代码注释
- 分层架构设计

## 部署建议

1. 使用Docker容器化部署
2. 配置Nginx反向代理
3. 使用Redis集群
4. 配置数据库读写分离
5. 添加监控和日志收集

## 联系方式

如有问题，请联系开发团队：<EMAIL>
