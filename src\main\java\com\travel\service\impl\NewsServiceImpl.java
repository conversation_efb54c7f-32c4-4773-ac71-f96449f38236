package com.travel.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.travel.common.BusinessException;
import com.travel.common.ResultCode;
import com.travel.entity.News;
import com.travel.mapper.NewsMapper;
import com.travel.service.NewsService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * 资讯信息服务实现
 * 
 * <AUTHOR> Team
 * @since 2024-01-01
 */
@Slf4j
@Service
public class NewsServiceImpl extends ServiceImpl<NewsMapper, News> implements NewsService {

    @Override
    public IPage<News> getNewsPage(Integer current, Integer size, Integer cityId) {
        Page<News> page = new Page<>(current, size);
        LambdaQueryWrapper<News> wrapper = new LambdaQueryWrapper<>();
        
        if (cityId != null) {
            wrapper.eq(News::getCityId, cityId);
        }
        
        wrapper.orderByDesc(News::getCreatedAt);
        return this.page(page, wrapper);
    }

    @Override
    public News getNewsDetail(Integer newsId) {
        News news = this.getById(newsId);
        if (news == null) {
            throw BusinessException.of(ResultCode.NOT_FOUND, "资讯不存在");
        }
        return news;
    }

    @Override
    public IPage<News> getNewsByCity(Integer current, Integer size, Integer cityId) {
        Page<News> page = new Page<>(current, size);
        LambdaQueryWrapper<News> wrapper = new LambdaQueryWrapper<News>()
                .eq(News::getCityId, cityId)
                .orderByDesc(News::getCreatedAt);
        return this.page(page, wrapper);
    }

    @Override
    public IPage<News> searchNews(Integer current, Integer size, String keyword) {
        Page<News> page = new Page<>(current, size);
        LambdaQueryWrapper<News> wrapper = new LambdaQueryWrapper<>();
        
        if (StringUtils.isNotBlank(keyword)) {
            wrapper.like(News::getTitle, keyword)
                   .or()
                   .like(News::getContent, keyword);
        }
        
        wrapper.orderByDesc(News::getCreatedAt);
        return this.page(page, wrapper);
    }
}
