package com.travel.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.travel.common.PageResult;
import com.travel.common.Result;
import com.travel.dto.review.CreateReviewRequest;
import com.travel.service.ReviewService;
import com.travel.vo.review.ReviewVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

/**
 * 评价控制器
 * 
 * <AUTHOR> Team
 * @since 2024-01-01
 */
@Tag(name = "评价管理", description = "评价相关接口")
@RestController
@RequestMapping("/review")
@RequiredArgsConstructor
public class ReviewController {

    private final ReviewService reviewService;

    @Operation(summary = "创建评价", description = "用户对产品进行评价")
    @PostMapping("/create")
    public Result<Void> createReview(
            @Parameter(description = "用户ID", required = true) @RequestParam Integer userId,
            @Valid @RequestBody CreateReviewRequest request) {
        reviewService.createReview(userId, request);
        return Result.success("评价成功");
    }

    @Operation(summary = "获取产品评价列表", description = "分页获取产品的评价列表")
    @GetMapping("/product/{productId}")
    public Result<PageResult<ReviewVO>> getProductReviews(
            @Parameter(description = "产品ID", required = true) @PathVariable Integer productId,
            @Parameter(description = "当前页码", example = "1") @RequestParam(defaultValue = "1") Integer current,
            @Parameter(description = "每页大小", example = "10") @RequestParam(defaultValue = "10") Integer size) {
        
        IPage<ReviewVO> page = reviewService.getProductReviews(productId, current, size);
        return Result.success(PageResult.of(page));
    }

    @Operation(summary = "获取用户评价列表", description = "分页获取用户的评价列表")
    @GetMapping("/user/{userId}")
    public Result<PageResult<ReviewVO>> getUserReviews(
            @Parameter(description = "用户ID", required = true) @PathVariable Integer userId,
            @Parameter(description = "当前页码", example = "1") @RequestParam(defaultValue = "1") Integer current,
            @Parameter(description = "每页大小", example = "10") @RequestParam(defaultValue = "10") Integer size) {
        
        IPage<ReviewVO> page = reviewService.getUserReviews(userId, current, size);
        return Result.success(PageResult.of(page));
    }

    @Operation(summary = "获取产品评价统计", description = "获取产品的评分和评价数量统计")
    @GetMapping("/stats/{productId}")
    public Result<Map<String, Object>> getProductReviewStats(
            @Parameter(description = "产品ID", required = true) @PathVariable Integer productId) {
        
        BigDecimal averageRating = reviewService.getProductAverageRating(productId);
        Long reviewCount = reviewService.getProductReviewCount(productId);
        
        Map<String, Object> stats = new HashMap<>();
        stats.put("averageRating", averageRating);
        stats.put("reviewCount", reviewCount);
        
        return Result.success(stats);
    }

    @Operation(summary = "检查用户是否已评价", description = "检查用户是否已对产品进行评价")
    @GetMapping("/check")
    public Result<Map<String, Object>> checkUserReviewed(
            @Parameter(description = "用户ID", required = true) @RequestParam Integer userId,
            @Parameter(description = "产品ID", required = true) @RequestParam Integer productId) {
        
        boolean hasReviewed = reviewService.hasUserReviewed(userId, productId);
        
        Map<String, Object> result = new HashMap<>();
        result.put("hasReviewed", hasReviewed);
        
        return Result.success(result);
    }

    @Operation(summary = "删除评价", description = "删除用户的评价")
    @DeleteMapping("/{reviewId}")
    public Result<Void> deleteReview(
            @Parameter(description = "评价ID", required = true) @PathVariable Integer reviewId,
            @Parameter(description = "用户ID", required = true) @RequestParam Integer userId) {
        reviewService.deleteReview(reviewId, userId);
        return Result.success("删除成功");
    }
}
