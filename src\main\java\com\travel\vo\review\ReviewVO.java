package com.travel.vo.review;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 评价信息VO
 * 
 * <AUTHOR> Team
 * @since 2024-01-01
 */
@Data
@Schema(description = "评价信息")
public class ReviewVO {

    @Schema(description = "评价ID", example = "1")
    private Integer reviewId;

    @Schema(description = "评价内容", example = "讲解很详细，值得推荐！")
    private String content;

    @Schema(description = "评分(1-5)", example = "5")
    private Integer rating;

    @Schema(description = "创建时间")
    private LocalDateTime createdAt;

    @Schema(description = "用户信息")
    private UserInfo user;

    @Schema(description = "产品信息")
    private ProductInfo product;

    @Data
    @Schema(description = "用户信息")
    public static class UserInfo {
        @Schema(description = "用户ID", example = "1")
        private Integer userId;

        @Schema(description = "用户昵称", example = "张三")
        private String nickname;

        @Schema(description = "头像URL", example = "https://example.com/avatar.jpg")
        private String avatarUrl;
    }

    @Data
    @Schema(description = "产品信息")
    public static class ProductInfo {
        @Schema(description = "产品ID", example = "1")
        private Integer productId;

        @Schema(description = "产品名称", example = "故宫博物院讲解包")
        private String name;

        @Schema(description = "封面图URL", example = "https://example.com/cover.jpg")
        private String coverUrl;
    }
}
